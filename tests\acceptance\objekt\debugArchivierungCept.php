<?php

use impro\objekte\Objekt;
use impro\objekte\ObjektCollectionPrepare;
use impro\rechnungen\Rechnung;
use impro\typen\ObjektEigenschaften;
use test\Settings;

$I = new ImproUser($scenario);
$I->wantTo('Debug Archivierung Problem für Datensatz 0536/24');

// Suche nach dem spezifischen Objekt
$obj = ObjektCollectionPrepare::create()
    ->selectAll()
    ->whereObjektNummer("=", "0536/24")
    ->get()->first();

if (!$obj) {
    $I->comment('Objekt 0536/24 nicht gefunden, erstelle Testobjekt');
    
    // Erstelle ein Testobjekt mit einer Rechnung
    $obj = Objekt::createPrototype();
    $obj->setObjektNummer("TEST-ARCHIV-" . time());
    $obj->save();
    
    $rechnung = Rechnung::createPrototype(Settings::getIB('Dresden'), null);
    $rechnung->setObjekt($obj)->save();
    
    $I->comment('Testobjekt erstellt: ' . $obj->getObjektNummer()->toString());
} else {
    $I->comment('Objekt 0536/24 gefunden: ID ' . $obj->getId());
}

// Debug: Aktueller Status vor Archivierung
$I->comment('=== STATUS VOR ARCHIVIERUNG ===');
$I->comment('Eigenschaften Archiviert: ' . ($obj->getEigenschaften()->getArchiviert() ? 'JA' : 'NEIN'));
$I->comment('Archiviert Datum: ' . ($obj->hasArchiviert() ? $obj->getArchiviert()->toString() : 'NICHT GESETZT'));
$I->comment('Eigenschaften Wert (Bitfeld): ' . $obj->getEigenschaften()->val());

// Prüfe ob Rechnungen vorhanden sind
$rechnungen = $obj->getRechnungen();
$I->comment('Anzahl Rechnungen: ' . count($rechnungen));

// Simuliere Archivierung wie im UI
$I->comment('=== ARCHIVIERUNG DURCHFÜHREN ===');

if (count($rechnungen) == 0) {
    $I->comment('Objekt ohne Rechnung - verwende archivieren() Methode');
    $obj->archivieren('Test Archivierung ohne Rechnung');
} else {
    $I->comment('Objekt mit Rechnung - setze Eigenschaften-Bit');
    $obj->getEigenschaften()->setArchiviert(true);
    $obj->save();
}

// Lade Objekt neu aus Datenbank
$obj = Objekt::load($obj->getId());

// Debug: Status nach Archivierung
$I->comment('=== STATUS NACH ARCHIVIERUNG ===');
$I->comment('Eigenschaften Archiviert: ' . ($obj->getEigenschaften()->getArchiviert() ? 'JA' : 'NEIN'));
$I->comment('Archiviert Datum: ' . ($obj->hasArchiviert() ? $obj->getArchiviert()->toString() : 'NICHT GESETZT'));
$I->comment('Eigenschaften Wert (Bitfeld): ' . $obj->getEigenschaften()->val());

// Prüfe Suchfilter
$I->comment('=== SUCHFILTER TEST ===');

// Test: Suche nur in aktiven Objekten (sollte das Objekt NICHT finden)
$activeObjects = ObjektCollectionPrepare::create()
    ->selectObjektNummer()
    ->whereEigenschaften('~&', ObjektEigenschaften::getIntByName(ObjektEigenschaften::FIELD_ARCHIVIERT))
    ->whereObjektNummer('=', $obj->getObjektNummer()->toString())
    ->get();

$I->comment('Objekt in aktiver Suche gefunden: ' . (count($activeObjects) > 0 ? 'JA (FEHLER!)' : 'NEIN (korrekt)'));

// Test: Suche nur in archivierten Objekten (sollte das Objekt finden)
$archivedObjects = ObjektCollectionPrepare::create()
    ->selectObjektNummer()
    ->whereEigenschaften('&', ObjektEigenschaften::getIntByName(ObjektEigenschaften::FIELD_ARCHIVIERT))
    ->whereObjektNummer('=', $obj->getObjektNummer()->toString())
    ->get();

$I->comment('Objekt in Archiv-Suche gefunden: ' . (count($archivedObjects) > 0 ? 'JA (korrekt)' : 'NEIN (FEHLER!)'));

// Prüfe Bitfeld-Wert
$archivBit = ObjektEigenschaften::getIntByName(ObjektEigenschaften::FIELD_ARCHIVIERT);
$I->comment('Archiv-Bit Wert: ' . $archivBit);
$I->comment('Eigenschaften & Archiv-Bit: ' . ($obj->getEigenschaften()->val() & $archivBit));

// Fazit
if (count($activeObjects) == 0 && count($archivedObjects) > 0) {
    $I->comment('=== ARCHIVIERUNG ERFOLGREICH ===');
} else {
    $I->comment('=== ARCHIVIERUNG FEHLERHAFT ===');
    $I->comment('Problem: Objekt ist nicht korrekt archiviert');
}