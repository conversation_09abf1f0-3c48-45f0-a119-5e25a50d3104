<?php

// Debug-Script für Archivierungsproblem
// Dieses Script kann direkt über HTTP aufgerufen werden

require_once 'src-gen/index.php';

use impro\objekte\Objekt;
use impro\objekte\ObjektCollectionPrepare;
use impro\rechnungen\Rechnung;
use impro\typen\ObjektEigenschaften;
use impro\typen\Datum;

echo "<h1>Debug Archivierung Problem</h1>\n";

try {
    // Suche nach dem spezifischen Objekt
    echo "<h2>1. Suche nach Objekt 0536/24</h2>\n";
    
    $obj = ObjektCollectionPrepare::create()
        ->selectAll()
        ->whereObjektNummer("=", "0536/24")
        ->get()->first();

    if (!$obj) {
        echo "<p>Objekt 0536/24 nicht gefunden, erstelle Testobjekt...</p>\n";
        
        // Erstelle ein Testobjekt mit einer Rechnung
        $obj = Objekt::createPrototype();
        $obj->setObjektNummer("TEST-ARCHIV-" . time());
        $obj->save();
        
        echo "<p>Testobjekt erstellt: " . $obj->getObjektNummer()->toString() . " (ID: " . $obj->getId() . ")</p>\n";
    } else {
        echo "<p>Objekt 0536/24 gefunden: ID " . $obj->getId() . "</p>\n";
    }

    // Debug: Aktueller Status vor Archivierung
    echo "<h2>2. Status vor Archivierung</h2>\n";
    echo "<ul>\n";
    echo "<li>Eigenschaften Archiviert: " . ($obj->getEigenschaften()->getArchiviert() ? 'JA' : 'NEIN') . "</li>\n";
    echo "<li>Archiviert Datum: " . ($obj->hasArchiviert() ? $obj->getArchiviert()->toString() : 'NICHT GESETZT') . "</li>\n";
    echo "<li>Eigenschaften Wert (Bitfeld): " . $obj->getEigenschaften()->val() . "</li>\n";
    
    // Prüfe ob Rechnungen vorhanden sind
    $rechnungen = $obj->getRechnungen();
    echo "<li>Anzahl Rechnungen: " . count($rechnungen) . "</li>\n";
    echo "</ul>\n";

    // Simuliere Archivierung wie im UI
    echo "<h2>3. Archivierung durchführen</h2>\n";

    if (count($rechnungen) == 0) {
        echo "<p>Objekt ohne Rechnung - verwende archivieren() Methode</p>\n";
        $obj->archivieren('Test Archivierung ohne Rechnung');
    } else {
        echo "<p>Objekt mit Rechnung - setze Eigenschaften-Bit</p>\n";
        
        // Debug: Zeige Eigenschaften vor Änderung
        echo "<p>Eigenschaften vor Änderung: " . $obj->getEigenschaften()->val() . "</p>\n";
        
        $obj->getEigenschaften()->setArchiviert(true);
        
        // Debug: Zeige Eigenschaften nach Änderung aber vor save()
        echo "<p>Eigenschaften nach setArchiviert(true): " . $obj->getEigenschaften()->val() . "</p>\n";
        
        $obj->save();
        
        echo "<p>save() aufgerufen</p>\n";
    }

    // Lade Objekt neu aus Datenbank
    $obj = Objekt::load($obj->getId());

    // Debug: Status nach Archivierung
    echo "<h2>4. Status nach Archivierung</h2>\n";
    echo "<ul>\n";
    echo "<li>Eigenschaften Archiviert: " . ($obj->getEigenschaften()->getArchiviert() ? 'JA' : 'NEIN') . "</li>\n";
    echo "<li>Archiviert Datum: " . ($obj->hasArchiviert() ? $obj->getArchiviert()->toString() : 'NICHT GESETZT') . "</li>\n";
    echo "<li>Eigenschaften Wert (Bitfeld): " . $obj->getEigenschaften()->val() . "</li>\n";
    echo "</ul>\n";

    // Prüfe Suchfilter
    echo "<h2>5. Suchfilter Test</h2>\n";

    // Test: Suche nur in aktiven Objekten (sollte das Objekt NICHT finden)
    $activeObjects = ObjektCollectionPrepare::create()
        ->selectObjektNummer()
        ->whereEigenschaften('~&', ObjektEigenschaften::getIntByName(ObjektEigenschaften::FIELD_ARCHIVIERT))
        ->whereObjektNummer('=', $obj->getObjektNummer()->toString())
        ->get();

    echo "<p>Objekt in aktiver Suche gefunden: " . (count($activeObjects) > 0 ? 'JA (FEHLER!)' : 'NEIN (korrekt)') . "</p>\n";

    // Test: Suche nur in archivierten Objekten (sollte das Objekt finden)
    $archivedObjects = ObjektCollectionPrepare::create()
        ->selectObjektNummer()
        ->whereEigenschaften('&', ObjektEigenschaften::getIntByName(ObjektEigenschaften::FIELD_ARCHIVIERT))
        ->whereObjektNummer('=', $obj->getObjektNummer()->toString())
        ->get();

    echo "<p>Objekt in Archiv-Suche gefunden: " . (count($archivedObjects) > 0 ? 'JA (korrekt)' : 'NEIN (FEHLER!)') . "</p>\n";

    // Prüfe Bitfeld-Wert
    $archivBit = ObjektEigenschaften::getIntByName(ObjektEigenschaften::FIELD_ARCHIVIERT);
    echo "<p>Archiv-Bit Wert: " . $archivBit . "</p>\n";
    echo "<p>Eigenschaften & Archiv-Bit: " . ($obj->getEigenschaften()->val() & $archivBit) . "</p>\n";

    // Fazit
    echo "<h2>6. Fazit</h2>\n";
    if (count($activeObjects) == 0 && count($archivedObjects) > 0) {
        echo "<p style='color: green;'><strong>ARCHIVIERUNG ERFOLGREICH</strong></p>\n";
    } else {
        echo "<p style='color: red;'><strong>ARCHIVIERUNG FEHLERHAFT</strong></p>\n";
        echo "<p>Problem: Objekt ist nicht korrekt archiviert</p>\n";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Fehler: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
